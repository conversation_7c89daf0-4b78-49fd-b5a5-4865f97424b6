<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$pageTitle = 'Admin Portal';
$message = '';
$messageType = '';

// Redirect if already logged in as admin
if (isLoggedIn() && isAdmin()) {
    header('Location: index.php');
    exit;
}

// Redirect non-admin users to main site
if (isLoggedIn() && !isAdmin()) {
    header('Location: ../index.php');
    exit;
}

// Enhanced security checks
$securityManager->enforceHTTPS();

// Check for suspicious activity
if ($securityManager->detectSuspiciousActivity()) {
    $message = 'Suspicious activity detected. Please try again later.';
    $messageType = 'danger';
}

// Rate limiting for admin login attempts
$rateLimitCheck = $securityManager->checkLoginRateLimit('admin_portal', true);
if (!$rateLimitCheck['allowed']) {
    $message = $rateLimitCheck['message'];
    $messageType = 'danger';
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && $rateLimitCheck['allowed']) {
    if (!$securityManager->validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $username = $securityManager->sanitizeInput($_POST['username']);
        $password = $_POST['password'];

        if ($userManager->login($username, $password)) {
            // Check if user is actually an admin
            if (isAdmin()) {
                // Reset login attempts on successful login
                $securityManager->resetLoginAttempts(true);

                // Log successful admin login
                $securityManager->logSecurityEvent('admin_login_success', [
                    'username' => $username,
                    'user_id' => $_SESSION['user_id']
                ]);

                header('Location: index.php');
                exit;
            } else {
                // User is not an admin, log them out and redirect
                $userManager->logout();
                $securityManager->recordFailedLogin($username, true);
                $message = 'Access denied. This portal is for administrators only.';
                $messageType = 'danger';
            }
        } else {
            $securityManager->recordFailedLogin($username, true);
            $message = 'Invalid credentials. Access denied.';
            $messageType = 'danger';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --admin-primary: #1e3a8a;
            --admin-secondary: #3b82f6;
            --admin-accent: #60a5fa;
            --admin-dark: #1e293b;
            --admin-light: #f8fafc;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
        }

        .admin-portal-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 20px;
        }

        .admin-portal-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(30, 58, 138, 0.1) 0%, transparent 50%);
        }

        .admin-login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            z-index: 10;
        }

        .admin-login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--admin-primary), var(--admin-secondary), var(--admin-accent));
        }

        .admin-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .admin-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 30px rgba(30, 58, 138, 0.3);
            position: relative;
            overflow: hidden;
        }

        .admin-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .admin-icon i {
            font-size: 2.5rem;
            color: white;
            z-index: 2;
            position: relative;
        }

        .admin-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--admin-dark);
            margin-bottom: 0.5rem;
        }

        .admin-subtitle {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 0;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            font-weight: 600;
            color: var(--admin-dark);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .admin-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(248, 250, 252, 0.8);
            position: relative;
        }

        .admin-input:focus {
            outline: none;
            border-color: var(--admin-secondary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: white;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 1.1rem;
            z-index: 2;
        }

        .admin-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .admin-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .admin-btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .admin-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(30, 58, 138, 0.4);
        }

        .admin-btn:active {
            transform: translateY(0);
        }

        .admin-btn span {
            position: relative;
            z-index: 2;
        }

        .security-notice {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1.5rem;
            text-align: center;
        }

        .security-notice i {
            color: #ef4444;
            margin-right: 0.5rem;
        }

        .security-notice span {
            color: #7f1d1d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #7f1d1d;
            border-left: 4px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="admin-portal-container">
        <div class="admin-portal-bg"></div>
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>

        <div class="admin-login-card">
            <div class="admin-header">
                <div class="admin-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="admin-title">Admin Portal</h1>
                <p class="admin-subtitle">Secure Administrator Access</p>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($_SESSION['admin_login_attempts'] < $maxAttempts): ?>
                <form method="POST" id="adminLoginForm">
                    <input type="hidden" name="csrf_token" value="<?php echo $securityManager->generateCSRFToken(); ?>">

                    <div class="form-group">
                        <label for="username" class="form-label">Administrator Username</label>
                        <div style="position: relative;">
                            <i class="fas fa-user-shield input-icon"></i>
                            <input type="text" class="admin-input" id="username" name="username" required
                                   placeholder="Enter admin username" autocomplete="username">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Secure Password</label>
                        <div style="position: relative;">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" class="admin-input" id="password" name="password" required
                                   placeholder="Enter admin password" autocomplete="current-password">
                        </div>
                    </div>

                    <button type="submit" class="admin-btn">
                        <span><i class="fas fa-sign-in-alt me-2"></i>Access Admin Portal</span>
                    </button>
                </form>
            <?php endif; ?>

            <div class="security-notice">
                <i class="fas fa-shield-alt"></i>
                <span>This is a restricted area. Unauthorized access is prohibited.</span>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Ripple effect on button click
        document.querySelector('.admin-btn').addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });

        // Enhanced form validation
        document.getElementById('adminLoginForm')?.addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('Please fill in all fields.');
                return false;
            }

            if (password.length < 6) {
                e.preventDefault();
                alert('Password must be at least 6 characters long.');
                return false;
            }
        });

        // Add subtle animations to inputs
        document.querySelectorAll('.admin-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>

<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('login.php');
}

$pageTitle = 'Manage Bookings';

// Handle booking status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $bookingId = (int)($_POST['booking_id'] ?? 0);

    switch ($action) {
        case 'confirm':
            if ($bookingManager->confirmBooking($bookingId)) {
                setFlashMessage('success', 'Booking confirmed successfully!');
            } else {
                setFlashMessage('error', 'Failed to confirm booking.');
            }
            break;

        case 'cancel':
            if ($bookingManager->cancelBooking($bookingId)) {
                setFlashMessage('success', 'Booking cancelled successfully!');
            } else {
                setFlashMessage('error', 'Failed to cancel booking.');
            }
            break;
    }

    redirect('bookings.php');
}

// Get all bookings with event and user details
$db->query('SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location,
           u.first_name, u.last_name, u.email, u.phone
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           JOIN users u ON b.user_id = u.id
           ORDER BY b.created_at DESC');
$bookings = $db->resultset();

// Get booking statistics
$db->query('SELECT
    COUNT(*) as total_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN 1 ELSE 0 END) as confirmed_bookings,
    SUM(CASE WHEN booking_status = "pending" THEN 1 ELSE 0 END) as pending_bookings,
    SUM(CASE WHEN booking_status = "cancelled" THEN 1 ELSE 0 END) as cancelled_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN total_amount ELSE 0 END) as total_revenue
    FROM bookings');
$stats = $db->single();

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--secondary-light);
        }

        .admin-header {
            background: var(--primary-gradient);
            color: white;
            padding: var(--space-2xl) 0;
            margin-top: 80px;
            border-radius: 0 0 30px 30px;
            position: relative;
            overflow: hidden;
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .admin-header .container-fluid {
            position: relative;
            z-index: 2;
        }

        .stats-card {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: var(--space-lg);
            box-shadow: var(--shadow-light);
            border-left: 4px solid var(--primary-color);
            transition: var(--transition-smooth);
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: var(--space-md);
        }

        .booking-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-light);
            transition: var(--transition-smooth);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .booking-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-xs);
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--border-radius-full);
            font-weight: 600;
            font-size: var(--text-sm);
        }

        .status-confirmed {
            background: rgba(16, 185, 129, 0.1);
            color: #065f46;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.1);
            color: #92400e;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-cancelled {
            background: rgba(239, 68, 68, 0.1);
            color: #991b1b;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .booking-reference {
            font-family: 'Courier New', monospace;
            background: var(--secondary-light);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--border-radius);
            font-size: var(--text-sm);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="bookings.php">Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-1"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">User Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Admin Header -->
    <section class="admin-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-ticket-alt me-3"></i>
                        Booking Management
                    </h1>
                    <p class="lead mb-0">Monitor and manage all event bookings</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-4">
        <div class="container-fluid">
            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-primary text-white">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h3 class="fw-bold mb-1"><?php echo number_format($stats->total_bookings); ?></h3>
                        <p class="text-muted mb-0">Total Bookings</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-success text-white">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="fw-bold mb-1"><?php echo number_format($stats->confirmed_bookings); ?></h3>
                        <p class="text-muted mb-0">Confirmed</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-warning text-white">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3 class="fw-bold mb-1"><?php echo number_format($stats->pending_bookings); ?></h3>
                        <p class="text-muted mb-0">Pending</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-info text-white">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <h3 class="fw-bold mb-1"><?php echo formatCurrency($stats->total_revenue); ?></h3>
                        <p class="text-muted mb-0">Revenue</p>
                    </div>
                </div>
            </div>

            <!-- Bookings Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">All Bookings</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($bookings)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Booking Reference</th>
                                                <th>Customer</th>
                                                <th>Event</th>
                                                <th>Date & Time</th>
                                                <th>Quantity</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Booking Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($bookings as $booking): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary"><?php echo $booking->booking_reference; ?></span>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($booking->first_name . ' ' . $booking->last_name); ?></strong><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($booking->email); ?></small><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($booking->phone); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($booking->title); ?></strong><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($booking->venue . ', ' . $booking->location); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php echo formatDate($booking->event_date); ?><br>
                                                        <small class="text-muted"><?php echo formatTime($booking->event_time); ?></small>
                                                    </td>
                                                    <td><?php echo $booking->quantity; ?></td>
                                                    <td><?php echo formatCurrency($booking->total_amount); ?></td>
                                                    <td>
                                                        <?php
                                                        $statusClass = 'secondary';
                                                        switch ($booking->booking_status) {
                                                            case 'confirmed': $statusClass = 'success'; break;
                                                            case 'pending': $statusClass = 'warning'; break;
                                                            case 'cancelled': $statusClass = 'danger'; break;
                                                        }
                                                        ?>
                                                        <span class="badge bg-<?php echo $statusClass; ?>">
                                                            <?php echo ucfirst($booking->booking_status); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo formatDate($booking->created_at); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-info"
                                                                    onclick="viewBookingDetails(<?php echo $booking->id; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>

                                                            <?php if ($booking->booking_status === 'pending'): ?>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="confirm">
                                                                    <input type="hidden" name="booking_id" value="<?php echo $booking->id; ?>">
                                                                    <button type="submit" class="btn btn-outline-success"
                                                                            onclick="return confirm('Confirm this booking?')">
                                                                        <i class="fas fa-check"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>

                                                            <?php if ($booking->booking_status !== 'cancelled'): ?>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="cancel">
                                                                    <input type="hidden" name="booking_id" value="<?php echo $booking->id; ?>">
                                                                    <button type="submit" class="btn btn-outline-danger"
                                                                            onclick="return confirm('Cancel this booking? This will restore ticket availability.')">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                    <h4>No Bookings Found</h4>
                                    <p class="text-muted">No bookings have been made yet.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        async function viewBookingDetails(bookingId) {
            try {
                // Show loading state
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.id = 'bookingDetailsModal';
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Booking Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Loading booking details...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();

                // Fetch booking details
                const response = await fetch(`get_booking_details.php?id=${bookingId}`);
                const data = await response.json();

                if (data.success) {
                    const booking = data.booking;
                    modal.querySelector('.modal-body').innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">Booking Information</h6>
                                <p><strong>Reference:</strong> ${booking.booking_reference}</p>
                                <p><strong>Status:</strong>
                                    <span class="badge bg-${booking.booking_status === 'confirmed' ? 'success' : booking.booking_status === 'pending' ? 'warning' : 'danger'}">
                                        ${booking.booking_status.charAt(0).toUpperCase() + booking.booking_status.slice(1)}
                                    </span>
                                </p>
                                <p><strong>Tickets:</strong> ${booking.quantity}</p>
                                <p><strong>Total Amount:</strong> ${booking.total_amount} FCFA</p>
                                <p><strong>Booking Date:</strong> ${new Date(booking.created_at).toLocaleDateString()}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">Customer Information</h6>
                                <p><strong>Name:</strong> ${booking.attendee_name}</p>
                                <p><strong>Email:</strong> ${booking.attendee_email}</p>
                                <p><strong>Phone:</strong> ${booking.attendee_phone || 'N/A'}</p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Event Information</h6>
                                <p><strong>Event:</strong> ${booking.event_title}</p>
                                <p><strong>Date:</strong> ${new Date(booking.event_date).toLocaleDateString()}</p>
                                <p><strong>Time:</strong> ${booking.event_time}</p>
                                <p><strong>Venue:</strong> ${booking.venue}</p>
                                <p><strong>Location:</strong> ${booking.location}</p>
                            </div>
                        </div>
                    `;
                } else {
                    modal.querySelector('.modal-body').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to load booking details. Please try again.
                        </div>
                    `;
                }

                // Clean up modal when closed
                modal.addEventListener('hidden.bs.modal', () => {
                    document.body.removeChild(modal);
                });

            } catch (error) {
                console.error('Error loading booking details:', error);
                alert('Failed to load booking details. Please try again.');
            }
        }
    </script>
</body>
</html>

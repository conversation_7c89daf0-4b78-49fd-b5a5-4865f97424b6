-- Security Enhancement Migration for ZARA-Events
-- Creates tables and indexes for enhanced security features

-- Create security_logs table for tracking security events
CREATE TABLE IF NOT EXISTS security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    event_data JSON,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at)
);

-- Create login_attempts table for more detailed tracking
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL, -- username or email
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    is_admin BOOLEAN DEFAULT FALSE,
    success BOOLEAN DEFAULT FALSE,
    failure_reason VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_identifier (identifier),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    INDEX idx_is_admin (is_admin)
);

-- Create session_tracking table for enhanced session management
CREATE TABLE IF NOT EXISTS session_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_id VARCHAR(128) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    logout_time TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_is_active (is_active)
);

-- Create admin_access_logs table for tracking admin activities
CREATE TABLE IF NOT EXISTS admin_access_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id INT,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    request_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_user_id (admin_user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Create rate_limiting table for tracking API/request limits
CREATE TABLE IF NOT EXISTS rate_limiting (
    id INT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL, -- IP address or user ID
    endpoint VARCHAR(100) NOT NULL,
    request_count INT DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_identifier_endpoint (identifier, endpoint),
    INDEX idx_identifier (identifier),
    INDEX idx_endpoint (endpoint),
    INDEX idx_window_start (window_start)
);

-- Add security-related columns to users table if they don't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_login_ip VARCHAR(45),
ADD COLUMN IF NOT EXISTS failed_login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS account_locked_until TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS two_factor_secret VARCHAR(32);

-- Add indexes for performance
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_last_login (last_login),
ADD INDEX IF NOT EXISTS idx_account_locked (account_locked_until),
ADD INDEX IF NOT EXISTS idx_role (role);

-- Create stored procedure for cleaning old security logs
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanOldSecurityLogs()
BEGIN
    -- Keep only last 30 days of security logs
    DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Keep only last 7 days of login attempts
    DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Clean inactive sessions older than 24 hours
    UPDATE session_tracking 
    SET is_active = FALSE, logout_time = NOW() 
    WHERE is_active = TRUE 
    AND last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    -- Remove old session records (keep last 30 days)
    DELETE FROM session_tracking WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Clean old rate limiting records
    DELETE FROM rate_limiting WHERE window_start < DATE_SUB(NOW(), INTERVAL 1 HOUR);
END //
DELIMITER ;

-- Create event to run cleanup daily
CREATE EVENT IF NOT EXISTS daily_security_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanOldSecurityLogs();

-- Insert initial security configuration
INSERT IGNORE INTO security_logs (event_type, event_data, ip_address, user_agent) 
VALUES ('system_init', '{"message": "Security system initialized"}', '127.0.0.1', 'System');

-- Create view for security dashboard
CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    DATE(created_at) as log_date,
    event_type,
    COUNT(*) as event_count,
    COUNT(DISTINCT ip_address) as unique_ips
FROM security_logs 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), event_type
ORDER BY log_date DESC, event_count DESC;

-- Create view for admin activity summary
CREATE OR REPLACE VIEW admin_activity_summary AS
SELECT 
    u.username,
    u.first_name,
    u.last_name,
    COUNT(aal.id) as total_actions,
    MAX(aal.created_at) as last_activity,
    COUNT(DISTINCT DATE(aal.created_at)) as active_days
FROM users u
LEFT JOIN admin_access_logs aal ON u.id = aal.admin_user_id
WHERE u.role = 'admin'
GROUP BY u.id, u.username, u.first_name, u.last_name
ORDER BY last_activity DESC;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON security_logs TO 'zara_events_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON login_attempts TO 'zara_events_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON session_tracking TO 'zara_events_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON admin_access_logs TO 'zara_events_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON rate_limiting TO 'zara_events_user'@'%';
-- GRANT EXECUTE ON PROCEDURE CleanOldSecurityLogs TO 'zara_events_user'@'%';

-- Success message
SELECT 'Security enhancement migration completed successfully!' as status;

<?php
/**
 * Admin User Management Script for ZARA-Events
 * Create, update, and manage admin users
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<h1>🔐 ZARA-Events Admin User Manager</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;line-height:1.6;}h1,h2,h3{color:#333;}.success{color:#28a745;background:#d4edda;padding:10px;border-radius:5px;margin:10px 0;}.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:5px;margin:10px 0;}.info{color:#0066cc;background:#e7f3ff;padding:10px;border-radius:5px;margin:10px 0;}.admin-card{background:#f8f9fa;border:1px solid #dee2e6;border-radius:8px;padding:15px;margin:10px 0;}.btn{display:inline-block;padding:8px 16px;margin:5px;text-decoration:none;border-radius:4px;font-weight:bold;}.btn-primary{background:#007bff;color:white;}.btn-success{background:#28a745;color:white;}.btn-warning{background:#ffc107;color:#212529;}.btn-danger{background:#dc3545;color:white;}</style>\n";

// Function to create or update admin user
function createOrUpdateAdmin($username, $email, $password, $firstName, $lastName, $isUpdate = false) {
    global $db;
    
    try {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        if ($isUpdate) {
            // Update existing admin
            $db->query('UPDATE users SET email = :email, password = :password, first_name = :first_name, last_name = :last_name, role = "admin", updated_at = NOW() WHERE username = :username');
        } else {
            // Create new admin
            $db->query('INSERT INTO users (username, email, password, first_name, last_name, role, created_at, updated_at) VALUES (:username, :email, :password, :first_name, :last_name, "admin", NOW(), NOW())');
        }
        
        $db->bind(':username', $username);
        $db->bind(':email', $email);
        $db->bind(':password', $hashedPassword);
        $db->bind(':first_name', $firstName);
        $db->bind(':last_name', $lastName);
        
        return $db->execute();
    } catch (Exception $e) {
        return false;
    }
}

// Function to promote user to admin
function promoteToAdmin($userId) {
    global $db;
    
    try {
        $db->query('UPDATE users SET role = "admin", updated_at = NOW() WHERE id = :id');
        $db->bind(':id', $userId);
        return $db->execute();
    } catch (Exception $e) {
        return false;
    }
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_admin':
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = $_POST['password'];
                $firstName = trim($_POST['first_name']);
                $lastName = trim($_POST['last_name']);
                
                if (createOrUpdateAdmin($username, $email, $password, $firstName, $lastName, false)) {
                    $message = "✅ Admin user '{$username}' created successfully!";
                    $messageType = 'success';
                } else {
                    $message = "❌ Failed to create admin user. Username or email might already exist.";
                    $messageType = 'error';
                }
                break;
                
            case 'update_admin':
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = $_POST['password'];
                $firstName = trim($_POST['first_name']);
                $lastName = trim($_POST['last_name']);
                
                if (createOrUpdateAdmin($username, $email, $password, $firstName, $lastName, true)) {
                    $message = "✅ Admin user '{$username}' updated successfully!";
                    $messageType = 'success';
                } else {
                    $message = "❌ Failed to update admin user.";
                    $messageType = 'error';
                }
                break;
                
            case 'promote_user':
                $userId = intval($_POST['user_id']);
                
                if (promoteToAdmin($userId)) {
                    $message = "✅ User promoted to admin successfully!";
                    $messageType = 'success';
                } else {
                    $message = "❌ Failed to promote user to admin.";
                    $messageType = 'error';
                }
                break;
                
            case 'reset_password':
                $username = trim($_POST['username']);
                $newPassword = $_POST['new_password'];
                
                try {
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    $db->query('UPDATE users SET password = :password, updated_at = NOW() WHERE username = :username AND role = "admin"');
                    $db->bind(':password', $hashedPassword);
                    $db->bind(':username', $username);
                    
                    if ($db->execute()) {
                        $message = "✅ Password reset successfully for admin '{$username}'!";
                        $messageType = 'success';
                    } else {
                        $message = "❌ Failed to reset password. Admin user not found.";
                        $messageType = 'error';
                    }
                } catch (Exception $e) {
                    $message = "❌ Error resetting password: " . $e->getMessage();
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Display message
if (!empty($message)) {
    echo "<div class='{$messageType}'>{$message}</div>\n";
}

// Get current users
$db->query('SELECT id, username, email, first_name, last_name, role, created_at FROM users ORDER BY role DESC, created_at DESC');
$users = $db->resultSet();

// Get admin users
$adminUsers = array_filter($users, function($user) {
    return $user->role === 'admin';
});

// Get regular users
$regularUsers = array_filter($users, function($user) {
    return $user->role === 'user';
});

echo "<h2>📊 Current Admin Users</h2>\n";

if (empty($adminUsers)) {
    echo "<div class='info'>ℹ️ No admin users found. Create one below.</div>\n";
} else {
    foreach ($adminUsers as $admin) {
        echo "<div class='admin-card'>\n";
        echo "<h3>👑 {$admin->first_name} {$admin->last_name}</h3>\n";
        echo "<p><strong>Username:</strong> {$admin->username}</p>\n";
        echo "<p><strong>Email:</strong> {$admin->email}</p>\n";
        echo "<p><strong>Created:</strong> {$admin->created_at}</p>\n";
        echo "<p><strong>Role:</strong> <span style='color:#28a745;font-weight:bold;'>ADMIN</span></p>\n";
        echo "</div>\n";
    }
}

echo "<h2>🔧 Quick Admin Setup</h2>\n";

// Quick setup for existing admin user
if (!empty($adminUsers)) {
    $existingAdmin = $adminUsers[0]; // Get first admin
    echo "<div class='info'>\n";
    echo "<h3>🚀 Quick Password Reset for Existing Admin</h3>\n";
    echo "<p>Reset password for: <strong>{$existingAdmin->username}</strong></p>\n";
    echo "<form method='POST' style='display:inline;'>\n";
    echo "<input type='hidden' name='action' value='reset_password'>\n";
    echo "<input type='hidden' name='username' value='{$existingAdmin->username}'>\n";
    echo "<input type='password' name='new_password' placeholder='New Password' required style='padding:8px;margin:5px;'>\n";
    echo "<button type='submit' class='btn btn-warning'>🔑 Reset Password</button>\n";
    echo "</form>\n";
    echo "</div>\n";
}

echo "<h2>➕ Create New Admin User</h2>\n";
echo "<form method='POST'>\n";
echo "<input type='hidden' name='action' value='create_admin'>\n";
echo "<table style='width:100%;'>\n";
echo "<tr><td>Username:</td><td><input type='text' name='username' required style='width:100%;padding:8px;'></td></tr>\n";
echo "<tr><td>Email:</td><td><input type='email' name='email' required style='width:100%;padding:8px;'></td></tr>\n";
echo "<tr><td>Password:</td><td><input type='password' name='password' required style='width:100%;padding:8px;'></td></tr>\n";
echo "<tr><td>First Name:</td><td><input type='text' name='first_name' required style='width:100%;padding:8px;'></td></tr>\n";
echo "<tr><td>Last Name:</td><td><input type='text' name='last_name' required style='width:100%;padding:8px;'></td></tr>\n";
echo "<tr><td colspan='2'><button type='submit' class='btn btn-success'>👑 Create Admin User</button></td></tr>\n";
echo "</table>\n";
echo "</form>\n";

if (!empty($regularUsers)) {
    echo "<h2>⬆️ Promote Existing User to Admin</h2>\n";
    echo "<form method='POST'>\n";
    echo "<input type='hidden' name='action' value='promote_user'>\n";
    echo "<select name='user_id' required style='padding:8px;margin:5px;'>\n";
    echo "<option value=''>Select a user to promote...</option>\n";
    foreach ($regularUsers as $user) {
        echo "<option value='{$user->id}'>{$user->username} ({$user->first_name} {$user->last_name}) - {$user->email}</option>\n";
    }
    echo "</select>\n";
    echo "<button type='submit' class='btn btn-primary'>⬆️ Promote to Admin</button>\n";
    echo "</form>\n";
}

echo "<h2>📋 All Users</h2>\n";
echo "<table style='width:100%;border-collapse:collapse;'>\n";
echo "<tr style='background:#f8f9fa;'><th style='padding:10px;border:1px solid #dee2e6;'>ID</th><th style='padding:10px;border:1px solid #dee2e6;'>Username</th><th style='padding:10px;border:1px solid #dee2e6;'>Email</th><th style='padding:10px;border:1px solid #dee2e6;'>Name</th><th style='padding:10px;border:1px solid #dee2e6;'>Role</th></tr>\n";

foreach ($users as $user) {
    $roleColor = $user->role === 'admin' ? '#28a745' : '#6c757d';
    $roleIcon = $user->role === 'admin' ? '👑' : '👤';
    echo "<tr>\n";
    echo "<td style='padding:10px;border:1px solid #dee2e6;'>{$user->id}</td>\n";
    echo "<td style='padding:10px;border:1px solid #dee2e6;'>{$user->username}</td>\n";
    echo "<td style='padding:10px;border:1px solid #dee2e6;'>{$user->email}</td>\n";
    echo "<td style='padding:10px;border:1px solid #dee2e6;'>{$user->first_name} {$user->last_name}</td>\n";
    echo "<td style='padding:10px;border:1px solid #dee2e6;color:{$roleColor};font-weight:bold;'>{$roleIcon} " . strtoupper($user->role) . "</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

echo "<h2>🔗 Quick Links</h2>\n";
echo "<p>\n";
echo "<a href='/admin/login.php' class='btn btn-primary'>🔐 Admin Portal</a>\n";
echo "<a href='/auth/login.php' class='btn btn-success'>👤 User Login</a>\n";
echo "<a href='/auth/register.php' class='btn btn-warning'>📝 User Registration</a>\n";
echo "</p>\n";

echo "<div class='info'>\n";
echo "<h3>💡 Tips:</h3>\n";
echo "<ul>\n";
echo "<li>Admin users can access the admin portal at <code>/admin/login.php</code></li>\n";
echo "<li>Regular users can only access user areas</li>\n";
echo "<li>Use strong passwords for admin accounts</li>\n";
echo "<li>You can promote any existing user to admin</li>\n";
echo "</ul>\n";
echo "</div>\n";
?>

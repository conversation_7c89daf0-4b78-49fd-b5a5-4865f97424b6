# PWA Configuration
RewriteEngine On

# Force HTTPS (disabled for local development)
# Uncomment the following lines for production deployment
# RewriteCond %{REQUEST_URI} !^/healthz\.php$
# RewriteCond %{REQUEST_URI} !^/health\.php$
# RewriteCond %{HTTP:X-Forwarded-Proto} !https
# RewriteCond %{HTTPS} off
# RewriteCond %{HTTP_HOST} !\.run\.app$
# RewriteCond %{HTTP_HOST} !^localhost
# RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Service Worker
<Files "sw.js">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
    Header set Service-Worker-Allowed "/"
</Files>

# Manifest
<Files "manifest.json">
    Header set Content-Type "application/manifest+json"
    Header set Cache-Control "public, max-age=604800"
</Files>

# PWA Icons
<FilesMatch "\.(png|ico|svg)$">
    Header set Cache-Control "public, max-age=2592000"
</FilesMatch>

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# MIME Types
AddType application/manifest+json .webmanifest
AddType application/manifest+json .json

<?php
/**
 * Security Initialization Script for ZARA-Events
 * Run this script to set up the enhanced security features
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<h1>ZARA-Events Security Enhancement Setup</h1>\n";
echo "<p>Initializing enhanced security features...</p>\n";

try {
    // Run security migration
    echo "<h2>1. Setting up security database tables...</h2>\n";
    
    $migrationSQL = file_get_contents('database/security_migration.sql');
    
    // Split the SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $migrationSQL)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $db->query($statement);
            $db->execute();
            $successCount++;
            echo "<p style='color: green;'>✓ Executed: " . substr($statement, 0, 50) . "...</p>\n";
        } catch (Exception $e) {
            $errorCount++;
            echo "<p style='color: orange;'>⚠ Warning: " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<p><strong>Database setup completed: {$successCount} statements executed, {$errorCount} warnings.</strong></p>\n";
    
    // Test security manager
    echo "<h2>2. Testing Security Manager...</h2>\n";
    
    require_once 'includes/security.php';
    
    // Test CSRF token generation
    $token = $securityManager->generateCSRFToken();
    if (!empty($token)) {
        echo "<p style='color: green;'>✓ CSRF token generation working</p>\n";
    } else {
        echo "<p style='color: red;'>✗ CSRF token generation failed</p>\n";
    }
    
    // Test input sanitization
    $testInput = "<script>alert('xss')</script>Test Input";
    $sanitized = $securityManager->sanitizeInput($testInput);
    if ($sanitized !== $testInput && !strpos($sanitized, '<script>')) {
        echo "<p style='color: green;'>✓ Input sanitization working</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Input sanitization failed</p>\n";
    }
    
    // Test rate limiting
    $rateLimitResult = $securityManager->checkLoginRateLimit('test_user', false);
    if (isset($rateLimitResult['allowed'])) {
        echo "<p style='color: green;'>✓ Rate limiting system working</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Rate limiting system failed</p>\n";
    }
    
    // Test admin middleware
    echo "<h2>3. Testing Admin Middleware...</h2>\n";
    
    require_once 'includes/admin_middleware.php';
    
    if (class_exists('AdminMiddleware')) {
        echo "<p style='color: green;'>✓ Admin middleware loaded successfully</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Admin middleware failed to load</p>\n";
    }
    
    // Create test admin user if none exists
    echo "<h2>4. Checking admin users...</h2>\n";
    
    $db->query('SELECT COUNT(*) as admin_count FROM users WHERE role = "admin"');
    $result = $db->single();
    
    if ($result && $result->admin_count > 0) {
        echo "<p style='color: green;'>✓ Admin users found: {$result->admin_count}</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ No admin users found. You may need to create one manually.</p>\n";
        echo "<p>To create an admin user, you can:</p>\n";
        echo "<ol>\n";
        echo "<li>Register a regular user account</li>\n";
        echo "<li>Update the user's role to 'admin' in the database</li>\n";
        echo "<li>Or run: <code>UPDATE users SET role = 'admin' WHERE username = 'your_username';</code></li>\n";
        echo "</ol>\n";
    }
    
    // Test security logging
    echo "<h2>5. Testing security logging...</h2>\n";
    
    try {
        $securityManager->logSecurityEvent('security_init_test', [
            'message' => 'Security system initialization test',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        echo "<p style='color: green;'>✓ Security logging working</p>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Security logging failed: " . $e->getMessage() . "</p>\n";
    }
    
    // Check file permissions
    echo "<h2>6. Checking file permissions...</h2>\n";
    
    $criticalFiles = [
        'includes/config.php',
        'includes/security.php',
        'includes/admin_middleware.php',
        'admin/login.php'
    ];
    
    foreach ($criticalFiles as $file) {
        if (file_exists($file)) {
            $perms = fileperms($file);
            $octal = substr(sprintf('%o', $perms), -4);
            
            if ($octal <= '0644') {
                echo "<p style='color: green;'>✓ {$file}: {$octal} (secure)</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠ {$file}: {$octal} (consider restricting permissions)</p>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ {$file}: File not found</p>\n";
        }
    }
    
    // Security recommendations
    echo "<h2>7. Security Recommendations</h2>\n";
    echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc;'>\n";
    echo "<h3>Production Security Checklist:</h3>\n";
    echo "<ul>\n";
    echo "<li>✓ Enable HTTPS in production</li>\n";
    echo "<li>✓ Set secure session cookie settings</li>\n";
    echo "<li>✓ Configure proper file permissions (644 for files, 755 for directories)</li>\n";
    echo "<li>✓ Set up regular security log monitoring</li>\n";
    echo "<li>✓ Configure database user with minimal required permissions</li>\n";
    echo "<li>✓ Set up automated backups</li>\n";
    echo "<li>✓ Configure firewall rules</li>\n";
    echo "<li>✓ Set up intrusion detection</li>\n";
    echo "<li>✓ Regular security updates</li>\n";
    echo "<li>✓ Monitor admin access logs</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // Environment-specific recommendations
    echo "<h3>Environment-Specific Settings:</h3>\n";
    
    if (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false) {
        echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>\n";
        echo "<h4>Development Environment Detected</h4>\n";
        echo "<p>For development, some security features are relaxed. In production:</p>\n";
        echo "<ul>\n";
        echo "<li>Enable HTTPS enforcement</li>\n";
        echo "<li>Set secure cookie flags</li>\n";
        echo "<li>Configure proper CSP headers</li>\n";
        echo "<li>Enable all security headers</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745;'>\n";
        echo "<h4>Production Environment</h4>\n";
        echo "<p>Ensure all security measures are properly configured for production use.</p>\n";
        echo "</div>\n";
    }
    
    echo "<h2>8. Setup Complete!</h2>\n";
    echo "<div style='background: #d4edda; padding: 20px; border: 2px solid #28a745; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724;'>✓ Security Enhancement Setup Completed Successfully!</h3>\n";
    echo "<p>Your ZARA-Events application now has enhanced security features including:</p>\n";
    echo "<ul>\n";
    echo "<li>Rate limiting for login attempts</li>\n";
    echo "<li>Enhanced session management</li>\n";
    echo "<li>Comprehensive security logging</li>\n";
    echo "<li>Admin access control and monitoring</li>\n";
    echo "<li>CSRF protection</li>\n";
    echo "<li>Input sanitization</li>\n";
    echo "<li>Security headers</li>\n";
    echo "</ul>\n";
    echo "<p><strong>Next Steps:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Test the admin login portal at <a href='/admin/login.php'>/admin/login.php</a></li>\n";
    echo "<li>Verify user authentication still works</li>\n";
    echo "<li>Review security logs in the database</li>\n";
    echo "<li>Configure production security settings</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border: 2px solid #dc3545; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>✗ Setup Failed</h3>\n";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><em>Security setup completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

code {
    background: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

ul, ol {
    padding-left: 20px;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 30px 0;
}
</style>

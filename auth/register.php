<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$pageTitle = 'Sign Up';
$message = '';
$messageType = '';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../user/dashboard.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!$securityManager->validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $userData = [
            'username' => $securityManager->sanitizeInput($_POST['username']),
            'email' => $securityManager->sanitizeInput($_POST['email']),
            'password' => $_POST['password'],
            'first_name' => $securityManager->sanitizeInput($_POST['first_name']),
            'last_name' => $securityManager->sanitizeInput($_POST['last_name']),
            'phone' => $securityManager->sanitizeInput($_POST['phone']),
            'address' => $securityManager->sanitizeInput($_POST['address']),
            'role' => 'user' // Force all new registrations to be users
        ];

        // Validate password confirmation
        if ($_POST['password'] !== $_POST['confirm_password']) {
            $message = 'Passwords do not match.';
            $messageType = 'danger';
        } else {
            $result = $userManager->register($userData);

            if (is_array($result)) {
                if (isset($result['success']) && $result['success']) {
                    // Log successful registration
                    $securityManager->logSecurityEvent('user_registration_success', [
                        'username' => $userData['username'],
                        'email' => $userData['email']
                    ]);

                    $message = 'Registration successful! You can now log in.';
                    $messageType = 'success';
                    // Clear form data
                    $userData = [];
                } else {
                    // Handle specific error messages
                    switch ($result['error']) {
                        case 'username_exists':
                            $message = 'Username already exists. Please choose a different username.';
                            break;
                        case 'email_exists':
                            $message = 'Email already exists. Please use a different email address.';
                            break;
                        case 'both_exist':
                            $message = 'Both username and email already exist. Please use different credentials.';
                            break;
                        case 'database_error':
                            $message = 'Failed to create account. Please try again.';
                            break;
                        default:
                            $message = $result['message'] ?? 'Registration failed. Please try again.';
                    }
                    $messageType = 'danger';
                }
            } else {
                // Handle legacy boolean return (backward compatibility)
                if ($result) {
                    $message = 'Registration successful! You can now log in.';
                    $messageType = 'success';
                    $userData = [];
                } else {
                    $message = 'Registration failed. Username or email may already exist.';
                    $messageType = 'danger';
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
    <link href="../assets/css/user-auth.css" rel="stylesheet">
</head>
<body class="auth-page">
    <!-- Floating Elements -->
    <div class="auth-floating-elements">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern auth-navbar fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../">Home</a>
                <a class="nav-link" href="login.php">Sign In</a>
            </div>
        </div>
    </nav>

    <!-- Registration Section -->
    <section class="hero-modern auth-hero d-flex align-items-center" style="min-height: 100vh; padding: 120px 0 60px;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="form-modern auth-form animate-on-scroll">
                        <div class="text-center mb-4">
                            <h2 class="auth-title">Join ZARA-Events</h2>
                            <p class="auth-subtitle">Create your account and discover amazing events in your area</p>
                        </div>

                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="registerForm">
                            <input type="hidden" name="csrf_token" value="<?php echo $securityManager->generateCSRFToken(); ?>">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="first_name" class="form-label">First Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-user"></i>
                                        </span>
                                        <input type="text" class="form-control form-control-modern auth-input"
                                               id="first_name" name="first_name" required
                                               value="<?php echo htmlspecialchars($userData['first_name'] ?? ''); ?>"
                                               placeholder="Enter your first name"
                                               aria-label="First Name"
                                               autocomplete="given-name">
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="last_name" class="form-label">Last Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-user"></i>
                                        </span>
                                        <input type="text" class="form-control form-control-modern auth-input"
                                               id="last_name" name="last_name" required
                                               value="<?php echo htmlspecialchars($userData['last_name'] ?? ''); ?>"
                                               placeholder="Enter your last name"
                                               aria-label="Last Name"
                                               autocomplete="family-name">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-at"></i>
                                        </span>
                                        <input type="text" class="form-control form-control-modern auth-input"
                                               id="username" name="username" required
                                               value="<?php echo htmlspecialchars($userData['username'] ?? ''); ?>"
                                               placeholder="Choose a username"
                                               aria-label="Username"
                                               autocomplete="username">
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control form-control-modern auth-input"
                                               id="email" name="email" required
                                               value="<?php echo htmlspecialchars($userData['email'] ?? ''); ?>"
                                               placeholder="Enter your email"
                                               aria-label="Email Address"
                                               autocomplete="email">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control form-control-modern auth-input"
                                               id="password" name="password" required
                                               placeholder="Create a password"
                                               aria-label="Password"
                                               autocomplete="new-password">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <small>Password must be at least 8 characters long</small>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control form-control-modern auth-input"
                                               id="confirm_password" name="confirm_password" required
                                               placeholder="Confirm your password"
                                               aria-label="Confirm Password"
                                               autocomplete="new-password">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-phone"></i>
                                        </span>
                                        <input type="tel" class="form-control form-control-modern auth-input"
                                               id="phone" name="phone"
                                               value="<?php echo htmlspecialchars($userData['phone'] ?? ''); ?>"
                                               placeholder="Enter your phone number"
                                               aria-label="Phone Number"
                                               autocomplete="tel">
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </span>
                                        <input type="text" class="form-control form-control-modern auth-input"
                                               id="address" name="address"
                                               value="<?php echo htmlspecialchars($userData['address'] ?? ''); ?>"
                                               placeholder="Enter your address"
                                               aria-label="Address"
                                               autocomplete="street-address">
                                    </div>
                                </div>
                            </div>

                            <!-- Hidden role field - all new registrations are users -->
                            <input type="hidden" name="role" value="user">

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="#" class="text-decoration-none">Terms of Service</a>
                                    and <a href="#" class="text-decoration-none">Privacy Policy</a>
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary-modern auth-btn w-100 mb-3">
                                <span>
                                    <i class="fas fa-user-plus me-2"></i>
                                    Create Account
                                </span>
                            </button>

                            <div class="text-center">
                                <p class="text-muted">
                                    Already have an account?
                                    <a href="login.php" class="text-decoration-none fw-bold">Sign in here</a>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');

            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match.');
                return false;
            }

            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }
        });

        // Real-time password confirmation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>

pip install mysql-connector-pythonRequest ID: 1d1a576a-0c8f-4d9f-ad3a-53b2f67ecae9# 🌐 ZARA-Events Google Cloud Run Web Deployment Guide

## 🚨 URGENT: Database Connection Fix Required

**The error "php_network_getaddresses: getaddrinfo for mysql failed" occurs because:**

❌ **Problem**: Your app is trying to connect to `mysql:3306` (Docker hostname) but Cloud Run needs an external database
✅ **Solution**: Set up an external database and configure proper environment variables

## 🎯 Quick Fix for Container Image

**Use the correct container image format:**

❌ **Wrong**: `https://hub.docker.com/r/zaramillion/zara-events`
✅ **Correct**: `zaramillion/zara-events:latest`

## �️ IMMEDIATE FIX - 3 Steps to Fix Your Error

### Step 1: Set Up Railway Database (5 minutes)
1. Go to [Railway.app](https://railway.app) and login
2. Click "New Project" → "Add MySQL"
3. Wait for deployment, then click on MySQL service
4. Go to "Connect" tab and copy connection details

### Step 2: Update Cloud Run Environment Variables
1. Go to your Cloud Run service in Google Cloud Console
2. Click "EDIT & DEPLOY NEW REVISION"
3. Go to "Variables & Secrets" tab
4. Add these variables (replace with your Railway details):
   ```
   DB_HOST=containers-us-west-xxx.railway.app
   DB_USER=root
   DB_PASS=your_railway_password
   DB_NAME=railway
   DB_PORT=3306
   ```

### Step 3: Import Database Schema
1. Connect to your Railway MySQL using any MySQL client
2. Import the file: `database/schema.sql`
3. Your app should now work!

**🎉 After these steps, your ZARA-Events app will be fully functional!**

## �📋 Step-by-Step Web Console Deployment

### Step 1: Access Google Cloud Console
1. Go to: https://console.cloud.google.com/
2. Select your project or create a new one
3. Navigate to **Cloud Run** from the menu

### Step 2: Create New Service
1. Click **"CREATE SERVICE"**
2. Select **"Deploy one revision from an existing container image"**

### Step 3: Container Configuration
**Container Image URL**:
```
zaramillion/zara-events:latest
```

**Service Settings**:
- **Service name**: `zara-events`
- **Region**: `us-central1` (or your preferred region)
- **CPU allocation**: CPU is only allocated during request processing
- **Ingress**: Allow all traffic
- **Authentication**: ✅ Allow unauthenticated invocations

### Step 4: Container Settings (IMPORTANT!)
Click **"CONTAINER, VARIABLES & SECRETS, CONNECTIONS, SECURITY"**

#### Container Tab:
- **Container port**: `80` ⚠️ (Very important!)
- **Memory**: `1 GiB`
- **CPU**: `1`
- **Request timeout**: `300` seconds
- **Maximum requests per container**: `80`

#### Variables & Secrets Tab:
Add these environment variables (⚠️ **CRITICAL - Database variables required**):

| Name | Value | Notes |
|------|-------|-------|
| `ENVIRONMENT` | `production` | Required |
| `DB_HOST` | `YOUR_DATABASE_HOST` | ⚠️ **MUST SET** - See database setup below |
| `DB_USER` | `YOUR_DATABASE_USER` | ⚠️ **MUST SET** |
| `DB_PASS` | `YOUR_DATABASE_PASSWORD` | ⚠️ **MUST SET** |
| `DB_NAME` | `event_booking_system` | ⚠️ **MUST SET** |
| `DB_PORT` | `3306` | Default MySQL port |
| `SMTP_HOST` | `smtp.gmail.com` | Email settings |
| `SMTP_PORT` | `587` | Email settings |
| `SMTP_USERNAME` | `<EMAIL>` | Email settings |
| `SMTP_PASSWORD` | `pvjc rjit ogxg ncce` | Email settings |
| `FROM_EMAIL` | `<EMAIL>` | Email settings |
| `FROM_NAME` | `ZARA-Events` | Email settings |
| `ADMIN_EMAIL` | `<EMAIL>` | Email settings |

### Step 5: Deploy
1. ⚠️ **IMPORTANT**: Set up database first (see below) and add DB environment variables
2. Click **"CREATE"**
3. Wait for deployment (usually 2-3 minutes)
4. You'll get a URL like: `https://zara-events-xxxxxxxxx-uc.a.run.app`

## 🚀 Quick Railway Database Setup

**If you already have Railway account, this is the fastest option:**

1. **Create Railway Database**:
   - Go to [Railway.app](https://railway.app)
   - Click "New Project" → "Add MySQL"
   - Wait for deployment (2-3 minutes)

2. **Get Connection Details**:
   - Click on MySQL service
   - Go to "Connect" tab
   - Copy the connection details

3. **Add to Cloud Run Environment Variables**:
   ```
   DB_HOST=containers-us-west-xxx.railway.app
   DB_USER=root
   DB_PASS=your_generated_password
   DB_NAME=railway
   DB_PORT=3306
   ```

4. **Import Database Schema**:
   - Use Railway's built-in MySQL client
   - Or connect via MySQL Workbench
   - Import the `database/schema.sql` file

## 🗄️ Database Setup Options (REQUIRED!)

⚠️ **Your app WILL NOT WORK without setting up an external database first!**

Since Cloud Run is stateless, you need an external database. Choose one option:

### Option A: Railway Database (Recommended - Free & Easy)
**✅ Best for quick setup with existing Railway account**

1. Go to [Railway.app](https://railway.app)
2. Create new project → Add MySQL database
3. Get connection details from Railway dashboard
4. Add these environment variables to Cloud Run:
   ```
   DB_HOST=containers-us-west-xxx.railway.app
   DB_USER=root
   DB_PASS=your_railway_password
   DB_NAME=railway
   DB_PORT=3306
   ```

### Option B: Google Cloud SQL (Production Ready)
**✅ Best for production with high availability**

1. Go to **Cloud SQL** in Google Cloud Console
2. Click **"CREATE INSTANCE"**
3. Choose **MySQL 8.0**
4. Configure:
   - **Instance ID**: `zara-events-db`
   - **Password**: Set a strong password (save it!)
   - **Region**: Same as your Cloud Run service
   - **Machine type**: `db-f1-micro` (for testing)
   - **Storage**: 10GB SSD
   - **Authorized networks**: Add `0.0.0.0/0` (for Cloud Run access)

5. After creation, get the **Public IP** and add these environment variables:
   ```
   DB_HOST=YOUR_CLOUD_SQL_PUBLIC_IP
   DB_USER=root
   DB_PASS=YOUR_PASSWORD
   DB_NAME=event_booking_system
   DB_PORT=3306
   ```

6. **Import Database Schema**: Use Cloud SQL console to import `database/schema.sql`

### Option C: Other External Database Services
- **PlanetScale** (MySQL-compatible, free tier)
- **AWS RDS** (if you have AWS account)
- **DigitalOcean Managed Database**
- **Aiven** (free tier available)

## 🚀 Alternative: One-Click Deploy Button

You can also create a one-click deploy button. Here's the URL format:

```
https://console.cloud.google.com/cloudshell/editor?cloudshell_git_repo=https://github.com/YOUR_REPO&cloudshell_image=gcr.io/cloudshell-images/cloudshell:latest&cloudshell_working_dir=.&cloudshell_open_in_editor=deploy-to-cloud-run.sh
```

## 🔧 Command Line Alternative

If you prefer command line:

1. **Install Google Cloud CLI**:
   ```bash
   # macOS
   brew install google-cloud-sdk

   # Windows
   # Download from: https://cloud.google.com/sdk/docs/install
   ```

2. **Authenticate**:
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

3. **Deploy**:
   ```bash
   gcloud run deploy zara-events \
     --image=zaramillion/zara-events:latest \
     --platform=managed \
     --region=us-central1 \
     --allow-unauthenticated \
     --port=80 \
     --memory=1Gi
   ```

## 🧪 Testing Your Deployment

After deployment, test these URLs:
- `https://your-service-url.run.app/` - Main app
- `https://your-service-url.run.app/about.php` - About page
- `https://your-service-url.run.app/contact.php` - Contact form
- `https://your-service-url.run.app/help-center.php` - Help center

## 🔍 Troubleshooting Common Issues

### Issue 1: "php_network_getaddresses: getaddrinfo for mysql failed" ⚠️
**This is YOUR current error!**

**Root Cause**: App is trying to connect to `mysql:3306` (Docker hostname) but Cloud Run needs external database

**Solution**:
1. Set up external database (Railway/Cloud SQL)
2. Add these environment variables to Cloud Run:
   ```y
   DB_HOST=your_database_host
   DB_USER=your_database_user
   DB_PASS=your_database_password
   DB_NAME=event_booking_system
   DB_PORT=3306
   ```
3. Redeploy the service

### Issue 2: "Container failed to start"
**Solution**: Check that container port is set to `80`

### Issue 3: "Service Unavailable"
**Solution**:
- Check logs in Cloud Run console
- Verify environment variables
- Ensure database connection

### Issue 4: "Database connection failed" (after setting up external DB)
**Solution**:
- Verify database host is accessible from internet
- Check database credentials are correct
- Ensure database has the required schema imported

### Issue 5: "Email not working"
**Solution**:
- Verify SMTP environment variables
- Check Gmail app password is correct

## 💰 Cost Estimation

**Cloud Run Pricing** (Pay-per-use):
- **Free tier**: 2 million requests/month
- **CPU**: $0.00002400 per vCPU-second
- **Memory**: $0.00000250 per GiB-second
- **Requests**: $0.40 per million requests

**Estimated monthly cost for low traffic**: $0-5

## 🎯 Quick Summary

1. ✅ Use `zaramillion/zara-events:latest` as image URL
2. ✅ Set container port to `80`
3. ✅ Add environment variables for email
4. ✅ Set up external database (Cloud SQL recommended)
5. ✅ Allow unauthenticated access
6. ✅ Test all endpoints after deployment

## 📞 Support

If you encounter issues:
- Check Cloud Run logs in the console
- Verify all environment variables
- Test database connectivity
- Contact: <EMAIL>

**🚀 Your ZARA-Events app will be live on Google Cloud Run in minutes!**

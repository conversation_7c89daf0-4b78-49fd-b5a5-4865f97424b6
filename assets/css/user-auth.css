/* Enhanced User Authentication Styles */
:root {
    --emerald-50: #ecfdf5;
    --emerald-100: #d1fae5;
    --emerald-200: #a7f3d0;
    --emerald-300: #6ee7b7;
    --emerald-400: #34d399;
    --emerald-500: #10b981;
    --emerald-600: #059669;
    --emerald-700: #047857;
    --emerald-800: #065f46;
    --emerald-900: #064e3b;

    --coral-50: #fef2f2;
    --coral-100: #fee2e2;
    --coral-200: #fecaca;
    --coral-300: #fca5a5;
    --coral-400: #f87171;
    --coral-500: #ef4444;
    --coral-600: #dc2626;
    --coral-700: #b91c1c;
    --coral-800: #991b1b;
    --coral-900: #7f1d1d;

    --gradient-emerald: linear-gradient(135deg, var(--emerald-400) 0%, var(--emerald-600) 100%);
    --gradient-coral: linear-gradient(135deg, var(--coral-400) 0%, var(--coral-600) 100%);
    --gradient-mixed: linear-gradient(135deg, var(--emerald-400) 0%, var(--coral-400) 50%, var(--emerald-600) 100%);
}

body.auth-page {
    background: linear-gradient(135deg, var(--emerald-50) 0%, var(--coral-50) 50%, var(--emerald-100) 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

body.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(52, 211, 153, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.navbar-modern.auth-navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(16, 185, 129, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.navbar-brand {
    background: var(--gradient-emerald);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 1.5rem;
}

.hero-modern.auth-hero {
    background: transparent;
    position: relative;
    z-index: 2;
}

.form-modern.auth-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 3rem;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(16, 185, 129, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.form-modern.auth-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-mixed);
}

.form-modern.auth-form:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 35px 70px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

.auth-title {
    background: var(--gradient-emerald);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.form-control-modern.auth-input {
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    padding: 1rem 1rem 1rem 3.5rem;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.8);
    position: relative;
}

.form-control-modern.auth-input:focus {
    outline: none;
    border-color: var(--emerald-400);
    box-shadow: 
        0 0 0 3px rgba(16, 185, 129, 0.1),
        0 8px 25px rgba(16, 185, 129, 0.15);
    background: white;
    transform: scale(1.02);
}

.form-control-modern.auth-input:hover {
    border-color: var(--emerald-300);
    transform: scale(1.01);
}

.input-group .input-group-text {
    background: transparent;
    border: 2px solid #e5e7eb;
    border-right: none;
    border-radius: 16px 0 0 16px;
    color: var(--emerald-600);
    transition: all 0.3s ease;
}

.input-group:focus-within .input-group-text {
    border-color: var(--emerald-400);
    color: var(--emerald-700);
}

.input-group:hover .input-group-text {
    border-color: var(--emerald-300);
}

.input-group .form-control-modern {
    border-left: none;
    border-radius: 0 16px 16px 0;
    padding-left: 1rem;
}

.btn-primary-modern.auth-btn {
    background: var(--gradient-emerald);
    border: none;
    border-radius: 16px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-primary-modern.auth-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.btn-primary-modern.auth-btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary-modern.auth-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
    background: linear-gradient(135deg, var(--emerald-500) 0%, var(--emerald-700) 100%);
}

.btn-primary-modern.auth-btn:active {
    transform: translateY(-1px) scale(1.01);
}

.btn-primary-modern.auth-btn span {
    position: relative;
    z-index: 2;
}

.form-check-input:checked {
    background-color: var(--emerald-500);
    border-color: var(--emerald-500);
}

.form-check-input:focus {
    border-color: var(--emerald-400);
    box-shadow: 0 0 0 0.25rem rgba(16, 185, 129, 0.25);
}

.text-decoration-none {
    color: var(--emerald-600);
    font-weight: 600;
    transition: all 0.3s ease;
}

.text-decoration-none:hover {
    color: var(--coral-500);
    text-decoration: underline !important;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: var(--emerald-800);
    border-radius: 12px;
    border-left: 4px solid var(--emerald-500);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: var(--coral-800);
    border-radius: 12px;
    border-left: 4px solid var(--coral-500);
}

/* Floating animation elements */
.auth-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    animation: float-gentle 8s ease-in-out infinite;
    opacity: 0.6;
}

.floating-shape:nth-child(1) {
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, var(--emerald-200), transparent);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-shape:nth-child(2) {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, var(--coral-200), transparent);
    top: 70%;
    right: 15%;
    animation-delay: 3s;
}

.floating-shape:nth-child(3) {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, var(--emerald-300), transparent);
    bottom: 20%;
    left: 20%;
    animation-delay: 6s;
}

@keyframes float-gentle {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg) scale(1); 
        opacity: 0.6;
    }
    50% { 
        transform: translateY(-30px) rotate(180deg) scale(1.1); 
        opacity: 0.8;
    }
}

/* Accessibility improvements */
.form-label {
    font-weight: 600;
    color: var(--emerald-800);
    margin-bottom: 0.5rem;
}

.form-text small {
    color: var(--emerald-600);
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
    .form-modern.auth-form {
        padding: 2rem;
        margin: 1rem;
    }
    
    .auth-title {
        font-size: 2rem;
    }
    
    .floating-shape {
        display: none;
    }
}

/* Focus indicators for accessibility */
.form-control-modern.auth-input:focus,
.btn-primary-modern.auth-btn:focus,
.form-check-input:focus {
    outline: 2px solid var(--emerald-400);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-modern.auth-form {
        border: 2px solid var(--emerald-600);
    }
    
    .form-control-modern.auth-input {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .form-modern.auth-form,
    .form-control-modern.auth-input,
    .btn-primary-modern.auth-btn,
    .floating-shape {
        animation: none;
        transition: none;
    }
    
    .form-control-modern.auth-input:focus,
    .form-control-modern.auth-input:hover,
    .btn-primary-modern.auth-btn:hover {
        transform: none;
    }
}

<?php
/**
 * Security Testing Script for ZARA-Events
 * Comprehensive testing of all authentication and security features
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';
require_once 'includes/admin_middleware.php';

echo "<h1>ZARA-Events Security Testing Suite</h1>\n";
echo "<p>Testing all authentication flows and security measures...</p>\n";

$testResults = [];
$passedTests = 0;
$totalTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $passedTests, $totalTests;
    
    $totalTests++;
    echo "<h3>Test {$totalTests}: {$testName}</h3>\n";
    
    try {
        $result = $testFunction();
        if ($result['success']) {
            $passedTests++;
            echo "<p style='color: green;'>✓ PASSED: {$result['message']}</p>\n";
            $testResults[$testName] = 'PASSED';
        } else {
            echo "<p style='color: red;'>✗ FAILED: {$result['message']}</p>\n";
            $testResults[$testName] = 'FAILED';
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ ERROR: {$e->getMessage()}</p>\n";
        $testResults[$testName] = 'ERROR';
    }
    
    echo "<hr>\n";
}

// Test 1: CSRF Token Generation and Validation
runTest("CSRF Token Security", function() {
    global $securityManager;
    
    $token1 = $securityManager->generateCSRFToken();
    $token2 = $securityManager->generateCSRFToken();
    
    // Tokens should be the same within the session
    if ($token1 !== $token2) {
        return ['success' => false, 'message' => 'CSRF tokens should be consistent within session'];
    }
    
    // Valid token should validate
    if (!$securityManager->validateCSRFToken($token1)) {
        return ['success' => false, 'message' => 'Valid CSRF token failed validation'];
    }
    
    // Invalid token should fail
    if ($securityManager->validateCSRFToken('invalid_token')) {
        return ['success' => false, 'message' => 'Invalid CSRF token passed validation'];
    }
    
    return ['success' => true, 'message' => 'CSRF token generation and validation working correctly'];
});

// Test 2: Input Sanitization
runTest("Input Sanitization", function() {
    global $securityManager;
    
    $maliciousInputs = [
        "<script>alert('xss')</script>",
        "'; DROP TABLE users; --",
        "<img src=x onerror=alert('xss')>",
        "javascript:alert('xss')"
    ];
    
    foreach ($maliciousInputs as $input) {
        $sanitized = $securityManager->sanitizeInput($input);
        
        // Check if dangerous content is removed/escaped
        if (strpos($sanitized, '<script>') !== false || 
            strpos($sanitized, 'javascript:') !== false ||
            strpos($sanitized, 'DROP TABLE') !== false) {
            return ['success' => false, 'message' => "Dangerous content not properly sanitized: {$input}"];
        }
    }
    
    return ['success' => true, 'message' => 'Input sanitization working correctly'];
});

// Test 3: Rate Limiting
runTest("Rate Limiting System", function() {
    global $securityManager;
    
    // Test normal rate limiting
    $result1 = $securityManager->checkLoginRateLimit('test_user', false);
    if (!$result1['allowed']) {
        return ['success' => false, 'message' => 'Rate limiting blocking legitimate requests'];
    }
    
    // Test admin rate limiting
    $result2 = $securityManager->checkLoginRateLimit('admin_test', true);
    if (!$result2['allowed']) {
        return ['success' => false, 'message' => 'Admin rate limiting blocking legitimate requests'];
    }
    
    return ['success' => true, 'message' => 'Rate limiting system functioning correctly'];
});

// Test 4: Session Management
runTest("Session Management", function() {
    global $securityManager;
    
    // Test session validation without active session
    if ($securityManager->validateSession()) {
        return ['success' => false, 'message' => 'Session validation should fail without active session'];
    }
    
    // Simulate active session
    $_SESSION['user_id'] = 999;
    $_SESSION['last_activity'] = time();
    
    if (!$securityManager->validateSession()) {
        return ['success' => false, 'message' => 'Session validation should pass with active session'];
    }
    
    // Test session timeout
    $_SESSION['last_activity'] = time() - 7200; // 2 hours ago
    
    if ($securityManager->validateSession()) {
        return ['success' => false, 'message' => 'Expired session should not validate'];
    }
    
    // Clean up
    unset($_SESSION['user_id'], $_SESSION['last_activity']);
    
    return ['success' => true, 'message' => 'Session management working correctly'];
});

// Test 5: Admin Access Control
runTest("Admin Access Control", function() {
    global $securityManager;
    
    // Test without admin session
    if ($securityManager->checkAdminAccess()) {
        return ['success' => false, 'message' => 'Admin access should be denied without admin session'];
    }
    
    // Test with user session (non-admin)
    $_SESSION['user_id'] = 999;
    $_SESSION['user_role'] = 'user';
    $_SESSION['last_activity'] = time();
    
    if ($securityManager->checkAdminAccess()) {
        return ['success' => false, 'message' => 'Admin access should be denied for regular users'];
    }
    
    // Test with admin session
    $_SESSION['user_role'] = 'admin';
    
    if (!$securityManager->checkAdminAccess()) {
        return ['success' => false, 'message' => 'Admin access should be granted for admin users'];
    }
    
    // Clean up
    unset($_SESSION['user_id'], $_SESSION['user_role'], $_SESSION['last_activity']);
    
    return ['success' => true, 'message' => 'Admin access control working correctly'];
});

// Test 6: Security Logging
runTest("Security Logging", function() {
    global $securityManager, $db;
    
    $testEventType = 'security_test_' . time();
    $testData = ['test' => 'data', 'timestamp' => time()];
    
    // Log a test event
    $securityManager->logSecurityEvent($testEventType, $testData);
    
    // Check if event was logged
    $db->query('SELECT * FROM security_logs WHERE event_type = :event_type ORDER BY created_at DESC LIMIT 1');
    $db->bind(':event_type', $testEventType);
    $logEntry = $db->single();
    
    if (!$logEntry) {
        return ['success' => false, 'message' => 'Security event was not logged to database'];
    }
    
    $loggedData = json_decode($logEntry->event_data, true);
    if ($loggedData['test'] !== 'data') {
        return ['success' => false, 'message' => 'Security event data was not logged correctly'];
    }
    
    return ['success' => true, 'message' => 'Security logging working correctly'];
});

// Test 7: Database Security Tables
runTest("Security Database Tables", function() {
    global $db;
    
    $requiredTables = [
        'security_logs',
        'login_attempts', 
        'session_tracking',
        'admin_access_logs',
        'rate_limiting'
    ];
    
    foreach ($requiredTables as $table) {
        $db->query("SHOW TABLES LIKE '{$table}'");
        if (!$db->single()) {
            return ['success' => false, 'message' => "Required security table '{$table}' does not exist"];
        }
    }
    
    return ['success' => true, 'message' => 'All security database tables exist'];
});

// Test 8: Authentication Pages Accessibility
runTest("Authentication Pages", function() {
    $authPages = [
        'auth/login.php',
        'auth/register.php',
        'admin/login.php'
    ];
    
    foreach ($authPages as $page) {
        if (!file_exists($page)) {
            return ['success' => false, 'message' => "Authentication page '{$page}' does not exist"];
        }
        
        // Check if page contains security enhancements
        $content = file_get_contents($page);
        if (strpos($content, 'auth-input') === false && strpos($content, 'admin-input') === false) {
            return ['success' => false, 'message' => "Page '{$page}' missing enhanced styling"];
        }
    }
    
    return ['success' => true, 'message' => 'All authentication pages exist and have enhanced styling'];
});

// Test 9: Security Headers
runTest("Security Headers", function() {
    $headers = headers_list();
    $headerString = implode(' ', $headers);
    
    $requiredHeaders = [
        'X-XSS-Protection',
        'X-Content-Type-Options',
        'X-Frame-Options'
    ];
    
    foreach ($requiredHeaders as $header) {
        if (strpos($headerString, $header) === false) {
            return ['success' => false, 'message' => "Required security header '{$header}' not set"];
        }
    }
    
    return ['success' => true, 'message' => 'Security headers are properly configured'];
});

// Test 10: File Permissions and Structure
runTest("File Security", function() {
    $criticalFiles = [
        'includes/config.php',
        'includes/security.php',
        'includes/admin_middleware.php'
    ];
    
    foreach ($criticalFiles as $file) {
        if (!file_exists($file)) {
            return ['success' => false, 'message' => "Critical file '{$file}' does not exist"];
        }
        
        if (!is_readable($file)) {
            return ['success' => false, 'message' => "Critical file '{$file}' is not readable"];
        }
    }
    
    return ['success' => true, 'message' => 'All critical security files exist and are accessible'];
});

// Display Results Summary
echo "<h2>Test Results Summary</h2>\n";
echo "<div style='background: " . ($passedTests === $totalTests ? '#d4edda' : '#f8d7da') . "; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
echo "<h3>Overall Result: {$passedTests}/{$totalTests} tests passed</h3>\n";

if ($passedTests === $totalTests) {
    echo "<p style='color: #155724; font-weight: bold;'>🎉 All security tests passed! Your authentication system is secure.</p>\n";
} else {
    echo "<p style='color: #721c24; font-weight: bold;'>⚠️ Some tests failed. Please review and fix the issues above.</p>\n";
}

echo "<h4>Detailed Results:</h4>\n";
echo "<ul>\n";
foreach ($testResults as $test => $result) {
    $color = $result === 'PASSED' ? 'green' : 'red';
    echo "<li style='color: {$color};'>{$test}: {$result}</li>\n";
}
echo "</ul>\n";
echo "</div>\n";

// Security Recommendations
echo "<h2>Security Recommendations</h2>\n";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0066cc; margin: 20px 0;'>\n";
echo "<h3>Next Steps:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Test User Registration:</strong> Create a new user account to verify the registration flow</li>\n";
echo "<li><strong>Test User Login:</strong> Log in with the new user account</li>\n";
echo "<li><strong>Test Admin Access:</strong> Try accessing admin pages without admin privileges</li>\n";
echo "<li><strong>Test Admin Login:</strong> Log in through the admin portal</li>\n";
echo "<li><strong>Monitor Security Logs:</strong> Check the security_logs table for events</li>\n";
echo "<li><strong>Test Rate Limiting:</strong> Try multiple failed login attempts</li>\n";
echo "<li><strong>Verify HTTPS:</strong> Ensure HTTPS is working in production</li>\n";
echo "<li><strong>Review Admin Logs:</strong> Check admin_access_logs for admin activities</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<hr>\n";
echo "<p><em>Security testing completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

ul, ol {
    padding-left: 20px;
}
</style>

<?php
/**
 * Enhanced Security Middleware for ZARA-Events
 * Provides comprehensive security features including rate limiting,
 * session management, and access control
 */

class SecurityManager {
    private $db;
    private $maxLoginAttempts = 5;
    private $lockoutDuration = 900; // 15 minutes
    private $sessionTimeout = 3600; // 1 hour
    
    public function __construct($database) {
        $this->db = $database;
        $this->initializeSecurity();
    }
    
    /**
     * Initialize security settings
     */
    private function initializeSecurity() {
        // Secure session configuration
        if (session_status() === PHP_SESSION_NONE) {
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', $this->isHTTPS());
            ini_set('session.cookie_samesite', 'Strict');
            ini_set('session.use_strict_mode', 1);
            ini_set('session.gc_maxlifetime', $this->sessionTimeout);
            ini_set('session.entropy_length', 32);
            ini_set('session.hash_function', 'sha256');

            session_start();
        }

        // Regenerate session ID periodically
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }

        // Track session in database
        $this->trackSession();
    }

    /**
     * Track session in database for enhanced security
     */
    private function trackSession() {
        if (isset($_SESSION['user_id'])) {
            try {
                // Check if session already tracked
                $this->db->query('SELECT id FROM session_tracking
                                 WHERE user_id = :user_id AND session_id = :session_id AND is_active = TRUE');
                $this->db->bind(':user_id', $_SESSION['user_id']);
                $this->db->bind(':session_id', session_id());

                if (!$this->db->single()) {
                    // Insert new session tracking record
                    $this->db->query('INSERT INTO session_tracking
                                     (user_id, session_id, ip_address, user_agent, login_time, last_activity)
                                     VALUES (:user_id, :session_id, :ip_address, :user_agent, NOW(), NOW())');
                    $this->db->bind(':user_id', $_SESSION['user_id']);
                    $this->db->bind(':session_id', session_id());
                    $this->db->bind(':ip_address', $this->getClientIP());
                    $this->db->bind(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
                    $this->db->execute();
                } else {
                    // Update last activity
                    $this->db->query('UPDATE session_tracking
                                     SET last_activity = NOW()
                                     WHERE user_id = :user_id AND session_id = :session_id');
                    $this->db->bind(':user_id', $_SESSION['user_id']);
                    $this->db->bind(':session_id', session_id());
                    $this->db->execute();
                }
            } catch (Exception $e) {
                // Fail silently to avoid breaking the application
                error_log("Session tracking error: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Check if connection is HTTPS
     */
    private function isHTTPS() {
        return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
               $_SERVER['SERVER_PORT'] == 443 ||
               (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
    }
    
    /**
     * Enforce HTTPS redirect
     */
    public function enforceHTTPS() {
        if (!$this->isHTTPS() && !$this->isDevelopment()) {
            $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            header("Location: $redirectURL", true, 301);
            exit();
        }
    }
    
    /**
     * Check if in development environment
     */
    private function isDevelopment() {
        return in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1']) ||
               strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0;
    }
    
    /**
     * Rate limiting for login attempts
     */
    public function checkLoginRateLimit($identifier, $isAdmin = false) {
        $sessionKey = $isAdmin ? 'admin_login_attempts' : 'login_attempts';
        $timeKey = $isAdmin ? 'admin_last_attempt' : 'last_attempt';
        
        if (!isset($_SESSION[$sessionKey])) {
            $_SESSION[$sessionKey] = 0;
            $_SESSION[$timeKey] = 0;
        }
        
        $timeSinceLastAttempt = time() - $_SESSION[$timeKey];
        
        // Reset attempts after lockout period
        if ($timeSinceLastAttempt >= $this->lockoutDuration) {
            $_SESSION[$sessionKey] = 0;
        }
        
        // Check if locked out
        if ($_SESSION[$sessionKey] >= $this->maxLoginAttempts && 
            $timeSinceLastAttempt < $this->lockoutDuration) {
            $remainingTime = $this->lockoutDuration - $timeSinceLastAttempt;
            return [
                'allowed' => false,
                'remaining_time' => $remainingTime,
                'message' => 'Too many failed attempts. Please try again in ' . 
                           ceil($remainingTime / 60) . ' minutes.'
            ];
        }
        
        return ['allowed' => true];
    }
    
    /**
     * Record failed login attempt
     */
    public function recordFailedLogin($identifier, $isAdmin = false) {
        $sessionKey = $isAdmin ? 'admin_login_attempts' : 'login_attempts';
        $timeKey = $isAdmin ? 'admin_last_attempt' : 'last_attempt';
        
        $_SESSION[$sessionKey] = ($_SESSION[$sessionKey] ?? 0) + 1;
        $_SESSION[$timeKey] = time();
        
        // Log security event
        $this->logSecurityEvent('failed_login', [
            'identifier' => $identifier,
            'is_admin' => $isAdmin,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'attempts' => $_SESSION[$sessionKey]
        ]);
    }
    
    /**
     * Reset login attempts on successful login
     */
    public function resetLoginAttempts($isAdmin = false) {
        $sessionKey = $isAdmin ? 'admin_login_attempts' : 'login_attempts';
        $timeKey = $isAdmin ? 'admin_last_attempt' : 'last_attempt';
        
        $_SESSION[$sessionKey] = 0;
        $_SESSION[$timeKey] = 0;
    }
    
    /**
     * Validate session and check for timeout
     */
    public function validateSession() {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity']) > $this->sessionTimeout) {
            $this->destroySession();
            return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * Enhanced role-based access control
     */
    public function checkAdminAccess() {
        if (!$this->validateSession()) {
            return false;
        }
        
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
            $this->logSecurityEvent('unauthorized_admin_access', [
                'user_id' => $_SESSION['user_id'] ?? null,
                'ip_address' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * Secure session destruction
     */
    public function destroySession() {
        $_SESSION = array();
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, 
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Log security events
     */
    public function logSecurityEvent($event_type, $data = []) {
        try {
            $this->db->query('INSERT INTO security_logs (event_type, event_data, ip_address, user_agent, created_at)
                             VALUES (:event_type, :event_data, :ip_address, :user_agent, NOW())');
            $this->db->bind(':event_type', $event_type);
            $this->db->bind(':event_data', json_encode($data));
            $this->db->bind(':ip_address', $this->getClientIP());
            $this->db->bind(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
            $this->db->execute();
        } catch (Exception $e) {
            // Fail silently to avoid breaking the application
            error_log("Security log error: " . $e->getMessage());
        }
    }
    
    /**
     * Enhanced CSRF protection
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token']) || 
            !isset($_SESSION['csrf_token_time']) ||
            (time() - $_SESSION['csrf_token_time']) > 3600) { // 1 hour expiry
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validate CSRF token with timing attack protection
     */
    public function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // Check token expiry
        if ((time() - $_SESSION['csrf_token_time']) > 3600) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Input sanitization with XSS protection
     */
    public function sanitizeInput($data, $allowHTML = false) {
        if (is_array($data)) {
            return array_map(function($item) use ($allowHTML) {
                return $this->sanitizeInput($item, $allowHTML);
            }, $data);
        }
        
        $data = trim($data);
        $data = stripslashes($data);
        
        if (!$allowHTML) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        } else {
            // Allow specific HTML tags if needed
            $data = strip_tags($data, '<p><br><strong><em><ul><ol><li>');
        }
        
        return $data;
    }
    
    /**
     * Check for suspicious activity patterns
     */
    public function detectSuspiciousActivity() {
        $ip = $this->getClientIP();
        
        // Check for rapid requests from same IP
        $this->db->query('SELECT COUNT(*) as request_count 
                         FROM security_logs 
                         WHERE ip_address = :ip 
                         AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)');
        $this->db->bind(':ip', $ip);
        $result = $this->db->single();
        
        if ($result && $result->request_count > 60) { // More than 60 requests per minute
            $this->logSecurityEvent('suspicious_activity', [
                'type' => 'rapid_requests',
                'request_count' => $result->request_count,
                'ip_address' => $ip
            ]);
            return true;
        }
        
        return false;
    }
}

// Initialize security manager
$securityManager = new SecurityManager($db);

// Global security functions for backward compatibility
function enhancedGenerateCSRFToken() {
    global $securityManager;
    return $securityManager->generateCSRFToken();
}

function enhancedValidateCSRFToken($token) {
    global $securityManager;
    return $securityManager->validateCSRFToken($token);
}

function enhancedSanitizeInput($data, $allowHTML = false) {
    global $securityManager;
    return $securityManager->sanitizeInput($data, $allowHTML);
}

function requireSecureAdmin() {
    global $securityManager;
    if (!$securityManager->checkAdminAccess()) {
        header('Location: /admin/login.php');
        exit();
    }
}
?>

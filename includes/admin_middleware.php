<?php
/**
 * Admin Authentication Middleware for ZARA-Events
 * Provides secure admin route protection with comprehensive logging and error handling
 */

require_once 'config.php';
require_once 'functions.php';
require_once 'security.php';

class AdminMiddleware {
    private $securityManager;
    private $db;
    
    public function __construct($securityManager, $database) {
        $this->securityManager = $securityManager;
        $this->db = $database;
    }
    
    /**
     * Main middleware function to protect admin routes
     */
    public function protect($requiredPermissions = []) {
        // Check if user is logged in
        if (!$this->securityManager->validateSession()) {
            $this->handleUnauthorizedAccess('not_logged_in');
            return false;
        }
        
        // Check if user has admin role
        if (!$this->isAdmin()) {
            $this->handleUnauthorizedAccess('insufficient_privileges');
            return false;
        }
        
        // Check specific permissions if required
        if (!empty($requiredPermissions) && !$this->hasPermissions($requiredPermissions)) {
            $this->handleUnauthorizedAccess('missing_permissions', $requiredPermissions);
            return false;
        }
        
        // Log admin access
        $this->logAdminAccess();
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * Check if current user is admin
     */
    private function isAdmin() {
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
    }
    
    /**
     * Check if admin has specific permissions
     */
    private function hasPermissions($requiredPermissions) {
        // For now, all admins have all permissions
        // This can be extended to support granular permissions
        return $this->isAdmin();
    }
    
    /**
     * Handle unauthorized access attempts
     */
    private function handleUnauthorizedAccess($reason, $additionalData = []) {
        // Log the unauthorized access attempt
        $this->securityManager->logSecurityEvent('unauthorized_admin_access', [
            'reason' => $reason,
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_role' => $_SESSION['user_role'] ?? null,
            'requested_url' => $_SERVER['REQUEST_URI'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'additional_data' => $additionalData
        ]);
        
        // Clear any existing session if it's compromised
        if ($reason === 'insufficient_privileges' && isset($_SESSION['user_id'])) {
            $this->securityManager->destroySession();
        }
        
        // Redirect based on the reason
        switch ($reason) {
            case 'not_logged_in':
                $this->redirectToLogin('Please log in to access the admin panel.');
                break;
                
            case 'insufficient_privileges':
                $this->redirectToHome('Access denied. You do not have administrator privileges.');
                break;
                
            case 'missing_permissions':
                $this->redirectToAdminDashboard('You do not have permission to access this resource.');
                break;
                
            default:
                $this->redirectToHome('Access denied.');
                break;
        }
    }
    
    /**
     * Log admin access for audit trail
     */
    private function logAdminAccess() {
        $action = $this->determineAction();
        $resource = $this->determineResource();
        
        try {
            $this->db->query('INSERT INTO admin_access_logs 
                             (admin_user_id, action, resource, resource_id, ip_address, user_agent, request_data, created_at)
                             VALUES (:admin_user_id, :action, :resource, :resource_id, :ip_address, :user_agent, :request_data, NOW())');
            
            $this->db->bind(':admin_user_id', $_SESSION['user_id']);
            $this->db->bind(':action', $action);
            $this->db->bind(':resource', $resource);
            $this->db->bind(':resource_id', $this->extractResourceId());
            $this->db->bind(':ip_address', $this->getClientIP());
            $this->db->bind(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
            $this->db->bind(':request_data', json_encode([
                'method' => $_SERVER['REQUEST_METHOD'],
                'query_params' => $_GET,
                'post_data' => $this->sanitizePostData($_POST)
            ]));
            
            $this->db->execute();
        } catch (Exception $e) {
            // Log error but don't break the application
            error_log("Admin access logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Determine the action being performed
     */
    private function determineAction() {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = $_SERVER['REQUEST_URI'];
        
        if (strpos($uri, '/admin/events.php') !== false) {
            if ($method === 'POST') {
                return isset($_POST['action']) ? $_POST['action'] : 'event_management';
            }
            return 'view_events';
        } elseif (strpos($uri, '/admin/bookings.php') !== false) {
            return 'view_bookings';
        } elseif (strpos($uri, '/admin/reports.php') !== false) {
            return 'view_reports';
        } elseif (strpos($uri, '/admin/index.php') !== false || strpos($uri, '/admin/') !== false) {
            return 'view_dashboard';
        }
        
        return 'admin_access';
    }
    
    /**
     * Determine the resource being accessed
     */
    private function determineResource() {
        $uri = $_SERVER['REQUEST_URI'];
        
        if (strpos($uri, '/admin/events.php') !== false) {
            return 'events';
        } elseif (strpos($uri, '/admin/bookings.php') !== false) {
            return 'bookings';
        } elseif (strpos($uri, '/admin/reports.php') !== false) {
            return 'reports';
        } elseif (strpos($uri, '/admin/') !== false) {
            return 'dashboard';
        }
        
        return 'admin_panel';
    }
    
    /**
     * Extract resource ID from request
     */
    private function extractResourceId() {
        if (isset($_GET['id'])) {
            return intval($_GET['id']);
        } elseif (isset($_POST['id'])) {
            return intval($_POST['id']);
        }
        return null;
    }
    
    /**
     * Sanitize POST data for logging
     */
    private function sanitizePostData($postData) {
        $sanitized = $postData;
        
        // Remove sensitive fields
        $sensitiveFields = ['password', 'confirm_password', 'csrf_token'];
        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, 
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Redirect to admin login with message
     */
    private function redirectToLogin($message = '') {
        if (!empty($message)) {
            $_SESSION['error_message'] = $message;
        }
        header('Location: /admin/login.php');
        exit();
    }
    
    /**
     * Redirect to home page with message
     */
    private function redirectToHome($message = '') {
        if (!empty($message)) {
            $_SESSION['error_message'] = $message;
        }
        header('Location: /index.php');
        exit();
    }
    
    /**
     * Redirect to admin dashboard with message
     */
    private function redirectToAdminDashboard($message = '') {
        if (!empty($message)) {
            $_SESSION['error_message'] = $message;
        }
        header('Location: /admin/index.php');
        exit();
    }
    
    /**
     * Check if admin session is about to expire
     */
    public function checkSessionExpiry() {
        if (isset($_SESSION['last_activity'])) {
            $timeLeft = $this->securityManager->sessionTimeout - (time() - $_SESSION['last_activity']);
            
            // Warn if less than 5 minutes left
            if ($timeLeft < 300 && $timeLeft > 0) {
                return [
                    'warning' => true,
                    'time_left' => $timeLeft,
                    'message' => 'Your session will expire in ' . ceil($timeLeft / 60) . ' minutes.'
                ];
            } elseif ($timeLeft <= 0) {
                return [
                    'expired' => true,
                    'message' => 'Your session has expired. Please log in again.'
                ];
            }
        }
        
        return ['status' => 'active'];
    }
    
    /**
     * Extend admin session
     */
    public function extendSession() {
        if ($this->isAdmin()) {
            $_SESSION['last_activity'] = time();
            return true;
        }
        return false;
    }
}

// Initialize admin middleware
$adminMiddleware = new AdminMiddleware($securityManager, $db);

/**
 * Helper function to protect admin routes
 */
function requireAdminAccess($permissions = []) {
    global $adminMiddleware;
    return $adminMiddleware->protect($permissions);
}

/**
 * Helper function to check session expiry
 */
function checkAdminSessionExpiry() {
    global $adminMiddleware;
    return $adminMiddleware->checkSessionExpiry();
}

/**
 * Helper function to extend session
 */
function extendAdminSession() {
    global $adminMiddleware;
    return $adminMiddleware->extendSession();
}
?>

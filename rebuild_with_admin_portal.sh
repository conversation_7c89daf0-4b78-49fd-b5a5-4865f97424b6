#!/bin/bash

echo "🔐 Rebuilding ZARA-Events with Enhanced Admin Portal"
echo "=================================================="

# Stop the current containers
echo "📦 Stopping current containers..."
docker-compose down

# Remove the old image to force rebuild
echo "🗑️  Removing old image..."
docker rmi zaramillion/zara-events:latest 2>/dev/null || true

# Build the new image with admin portal
echo "🔨 Building new image with admin portal..."
docker-compose build --no-cache

# Start the containers
echo "🚀 Starting containers..."
docker-compose up -d

# Wait a moment for containers to start
echo "⏳ Waiting for containers to start..."
sleep 10

# Check if containers are running
echo "✅ Checking container status..."
docker-compose ps

echo ""
echo "🎉 Rebuild complete!"
echo ""
echo "🌐 Your ZARA-Events application is now available at:"
echo "   - Main site: http://localhost:7823"
echo "   - User login: http://localhost:7823/auth/login.php"
echo "   - Admin portal: http://localhost:7823/admin/login.php"
echo "   - phpMyAdmin: http://localhost:8081"
echo ""
echo "🔐 Admin Portal Features:"
echo "   - Metallic-blue gradient design with ripple animations"
echo "   - Enhanced security with rate limiting"
echo "   - Comprehensive audit logging"
echo "   - Role-based access control"
echo ""
echo "🎨 User Portal Features:"
echo "   - Emerald and coral color palette"
echo "   - Zoom animations on focus"
echo "   - Enhanced accessibility"
echo "   - Clean, welcoming interface"
echo ""
echo "📋 Next Steps:"
echo "   1. Test the admin portal: http://localhost:7823/admin/login.php"
echo "   2. Create an admin user (see instructions below)"
echo "   3. Test the enhanced user authentication"
echo "   4. Run security initialization: docker exec zara_events_web php security_init.php"
echo ""
echo "👤 To create an admin user:"
echo "   1. Register a regular user account first"
echo "   2. Access phpMyAdmin: http://localhost:8081"
echo "   3. Run: UPDATE users SET role = 'admin' WHERE username = 'your_username';"
echo ""
echo "🔍 To check logs:"
echo "   docker logs zara_events_web"
echo ""
echo "✨ Your dual-portal authentication system is ready!"
